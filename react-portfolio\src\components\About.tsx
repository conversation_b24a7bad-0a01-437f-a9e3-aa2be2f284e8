import React, { useEffect, useState } from 'react';
import './About.css';

interface Skill {
  name: string;
  icon: string;
  color: string;
}

const About: React.FC = () => {
  const [skills, setSkills] = useState<Skill[]>([  // Initial skills state with static data. This can be replaced with API data or other dynamic data sources.
    { name: 'React', icon: '⚛️', color: '#61DAFB' },
    { name: 'TypeScript', icon: '📘', color: '#3178C6' },
    { name: 'Node.js', icon: '🟢', color: '#339933' },
    { name: 'Python', icon: '🐍', color: '#3776AB' },
    { name: 'Design', icon: '🎨', color: '#FF6B6B' },
    { name: 'Database', icon: '🗄️', color: '#4DB33D' },
  ]);  // Initial skills state with static data. This can be replaced with API data or other dynamic data sources.

  // Get skills from skills.json api
  useEffect(() => {  // Fetch skills from API on component mount. This can be replaced with other data sources or logic.
    fetch('http://localhost:3000/api/skills')
      .then(response => response.json())
      .then(data => {
        setSkills(data);
      })
      .catch(error => {
        console.error('Error fetching skills:', error);
        // Keep the default skills if API fails
      });
  }, []);

  return (
    <section id="about" className="about">
      <div className="containeer">
        <div className="section-header">
          <h2 className="section-title">My Name </h2>
          <p className="section-subtitle">Passionate about creating amazing digital experiences</p>
        </div>
        
        <div className="about-content">
          <div className="about-text">
            <p className="about-description">
              I'm a full-stack developer with a passion for creating beautiful, functional, 
              and user-friendly applications. With expertise in modern web technologies, 
              I bring ideas to life through clean code and innovative design.
            </p>
            <p className="about-description">
              When I'm not coding, you can find me exploring new technologies, 
              contributing to open-source projects, or sharing knowledge with the developer community.
            </p>
          </div>
          
          <div className="skills-grid">
            {skills.map((skill, index) => (
              <div 
                key={skill.name} 
                className="skill-card"
                style={{ '--skill-color': skill.color } as React.CSSProperties}
              >
                <div className="skill-icon">{skill.icon}</div>
                <span className="skill-name">{skill.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
