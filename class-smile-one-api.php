<?php
class Smile_One_API {
    private static $instance;
    private $settings;
    private $api_url;

    public static function init() {
        if (!isset(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        $this->settings = get_option('smile_one_wc_settings');
        $this->api_url = 'https://www.smile.one/' . $this->settings['country'];
        
        add_action('admin_menu', array($this, 'add_settings_page'));
        add_action('admin_init', array($this, 'register_settings'));
    }

    public function add_settings_page() {
        add_submenu_page(
            'woocommerce',
            'Smile One Settings',
            'Smile One API',
            'manage_options',
            'smile-one-wc-settings',
            array($this, 'render_settings_page')
        );
    }

    public function register_settings() {
        register_setting('smile_one_wc_settings_group', 'smile_one_wc_settings');
        
        add_settings_section(
            'smile_one_wc_api_section',
            'API Credentials',
            array($this, 'render_api_section'),
            'smile-one-wc-settings'
        );
        
        add_settings_field(
            'email',
            'Smile One Email',
            array($this, 'render_email_field'),
            'smile-one-wc-settings',
            'smile_one_wc_api_section'
        );
        
        add_settings_field(
            'uid',
            'Smile One UID',
            array($this, 'render_uid_field'),
            'smile-one-wc-settings',
            'smile_one_wc_api_section'
        );
        
        add_settings_field(
            'secret_key',
            'Secret Key',
            array($this, 'render_secret_key_field'),
            'smile-one-wc-settings',
            'smile_one_wc_api_section'
        );
        
        add_settings_field(
            'country',
            'Country Code',
            array($this, 'render_country_field'),
            'smile-one-wc-settings',
            'smile_one_wc_api_section'
        );
    }

    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1>Smile One API Settings</h1>
            <form method="post" action="options.php">
                <?php
                settings_fields('smile_one_wc_settings_group');
                do_settings_sections('smile-one-wc-settings');
                submit_button();
                ?>
            </form>
            
            <h2>API Connection Test</h2>
            <button id="test-smile-api" class="button button-secondary">Test Connection</button>
            <div id="api-test-result" style="margin-top: 10px;"></div>
            
            <script>
            jQuery(document).ready(function($) {
                $('#test-smile-api').click(function() {
                    $('#api-test-result').html('<p>Testing connection to Smile One API...</p>');
                    
                    $.post(ajaxurl, {
                        action: 'test_smile_one_api',
                        security: '<?php echo wp_create_nonce("smile_one_api_test"); ?>'
                    }, function(response) {
                        if (response.success) {
                            $('#api-test-result').html('<div class="notice notice-success"><p>Connection successful! Available points: ' + response.data.points + '</p></div>');
                        } else {
                            $('#api-test-result').html('<div class="notice notice-error"><p>Error: ' + response.data.message + '</p></div>');
                        }
                    });
                });
            });
            </script>
        </div>
        <?php
    }

    public function render_api_section() {
        echo '<p>Enter your Smile One API credentials below. Contact Smile One support if you don\'t have these details.</p>';
    }

    public function render_email_field() {
        $settings = get_option('smile_one_wc_settings');
        echo '<input type="email" name="smile_one_wc_settings[email]" value="' . esc_attr($settings['email']) . '" class="regular-text">';
    }

    public function render_uid_field() {
        $settings = get_option('smile_one_wc_settings');
        echo '<input type="text" name="smile_one_wc_settings[uid]" value="' . esc_attr($settings['uid']) . '" class="regular-text">';
    }

    public function render_secret_key_field() {
        $settings = get_option('smile_one_wc_settings');
        echo '<input type="password" name="smile_one_wc_settings[secret_key]" value="' . esc_attr($settings['secret_key']) . '" class="regular-text">';
    }

    public function render_country_field() {
        $settings = get_option('smile_one_wc_settings');
        echo '<select name="smile_one_wc_settings[country]" class="regular-text">';
        echo '<option value="ph" ' . selected($settings['country'], 'ph', false) . '>Philippines</option>';
        echo '<option value="br" ' . selected($settings['country'], 'br', false) . '>Brazil</option>';
        echo '<option value="ru" ' . selected($settings['country'], 'ru', false) . '>Russia</option>';
        echo '</select>';
    }

    public function generate_sign($params) {
        $secret_key = $this->settings['secret_key'];
        ksort($params);
        $str = '';
        
        foreach ($params as $k => $v) {
            $str .= $k . '=' . $v . '&';
        }
        
        $str .= $secret_key;
        return md5(md5($str));
    }

    public function make_api_request($endpoint, $params) {
        $params['time'] = time();
        $params['sign'] = $this->generate_sign($params);
        
        $url = $this->api_url . $endpoint;
        
        $args = array(
            'body' => $params,
            'timeout' => '30',
            'headers' => array(
                'Content-Type' => 'application/x-www-form-urlencoded'
            )
        );
        
        $response = wp_remote_post($url, $args);
        
        if (is_wp_error($response)) {
            return array(
                'status' => 500,
                'message' => $response->get_error_message()
            );
        }
        
        return json_decode(wp_remote_retrieve_body($response), true);
    }

    public static function test_api_connection() {
        check_ajax_referer('smile_one_api_test', 'security');
        
        $api = self::init();
        $params = array(
            'uid' => $api->settings['uid'],
            'email' => $api->settings['email'],
            'product' => 'mobilelegends'
        );
        
        $response = $api->make_api_request('/smilecoin/api/querypoints', $params);
        
        if ($response['status'] == 200) {
            wp_send_json_success(array(
                'points' => $response['smile_points']
            ));
        } else {
            wp_send_json_error(array(
                'message' => $response['message'] ?? 'API request failed'
            ));
        }
    }
}

add_action('wp_ajax_test_smile_one_api', array('Smile_One_API', 'test_api_connection'));