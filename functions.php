<?php

/**
 * Append data to Google Sheet
 */
function net_append_to_google_sheet($spreadsheetId, $worksheetName, $data)
{
    $credentials_path = GCI_PLUGIN_ROOT . 'credentials.json'; // Get the saved path
    if (empty($credentials_path) || !file_exists($credentials_path)) {
        echo ('GSI: Credentials file not found.');
        return false;
    }

    $client = new \Google\Client();
    $client->setApplicationName('Your Application Name');
    $client->setAuthConfig($credentials_path);
    $client->setScopes([\Google\Service\Sheets::SPREADSHEETS]);


    $service = new \Google\Service\Sheets($client);

    if (empty($spreadsheetId) || empty($worksheetName)) {
        error_log('GSI: Spreadsheet ID or Worksheet Name not set.');
        return false;
    }
    $range = $worksheetName . '!A:Z'; // Adjust range as needed

    $values = [
        $data
    ];

    $body = new \Google\Service\Sheets\ValueRange([
        'values' => $values
    ]);

    $params = [
        'valueInputOption' => 'USER_ENTERED'
    ];
    try {
        $result = $service->spreadsheets_values->append($spreadsheetId, $range, $body, $params);
        return true;
    } catch (\Exception $e) {
        echo '<pre> TEST' . print_r($service->spreadsheets_values, true) . '</pre>';
        error_log('GSI: Google Sheets API Error: ' . $e->getMessage());
        return false;
    }
}

add_action('elementor_pro/forms/new_record', 'net_elementor_pro_forms_new_record', 10, 2);
function net_elementor_pro_forms_new_record($record, $handler)
{
    $form_name = $record->get_form_settings('form_name');

    if ('Customized Trip Request' == $form_name) {
        net_customized_trip_request($record, $handler);
    } else if ('Send Inquiry' == $form_name) {
        net_send_inquiry($record, $handler);
    }
}

function net_customized_trip_request($record, $handler)
{

    // Retrieve form data
    $raw_fields = $record->get('fields');
    $meta_fields = $record->get('meta');

    $form_data = [];

    foreach ($raw_fields as $id => $field) {
        $form_data[$id] = $field['value']; // Store field ID and value
    }

    $meta_data = [];

    foreach ($meta_fields as $id => $field) {
        $meta_data[$id] = $field['value']; // Store field ID and value
    }
    $customer_name = $form_data['name'];
    $customer_email = $form_data['email'];
    $customer_phone = $form_data['phone_whatsapp'];
    $customer_rooms = $form_data['rooms'];
    $customer_persons = $form_data['persons'];
    $customer_departure_from = $form_data['departure_from'];
    $customer_visit_to = $form_data['visit_to'];
    $customer_start_date = $form_data['start_date'];
    $customer_end_date = $form_data['end_date'];
    $customer_message = $form_data['message'];
    $date = $meta_data['date'];
    $time = $meta_data['time'];
    $page_url = $meta_data['page_url'];

    $data_to_sheet = [
        $date,
        $time,
        $customer_name,
        $customer_email,
        $customer_phone,
        $customer_rooms,
        $customer_persons,
        $customer_departure_from,
        $customer_visit_to,
        $customer_start_date,
        $customer_end_date,
        $customer_message,
        $page_url
    ];
    $spreadsheetId = '1rfcamqss2dur0QGGaARxROpG4l70dKQ7tfM_BWgq9zs';
    $worksheetName = 'Sheet1';
    net_append_to_google_sheet($spreadsheetId, $worksheetName, $data_to_sheet);
}

function net_send_inquiry($record, $handler)
{

    // Retrieve form data
    $raw_fields = $record->get('fields');
    $meta_fields = $record->get('meta');

    $form_data = [];
    foreach ($raw_fields as $id => $field) {
        $form_data[$id] = $field['value']; // Store field ID and value
    }

    $meta_data = [];
    foreach ($meta_fields as $id => $field) {
        $meta_data[$id] = $field['value']; // Store field ID and value
    }

    $customer_name = $form_data['name'];
    $customer_email = $form_data['email'];
    $customer_phone = $form_data['phone_whatsapp'];
    $customer_message = $form_data['message'];
    $date = $meta_data['date'];
    $time = $meta_data['time'];
    $page_url = $meta_data['page_url'];

    $data_to_sheet = [
        $date,
        $time,
        $customer_name,
        $customer_email,
        $customer_phone,
        $customer_message,
        $page_url
    ];
    $spreadsheetId = '1CFhKa-LcdP_Y_0rUsti8c32BqNw5YBaIveAUrNK2B9c';
    $worksheetName = 'Sheet1';
    net_append_to_google_sheet($spreadsheetId, $worksheetName, $data_to_sheet);
}

// add_action('woocommerce_thankyou', 'net_save_order_data_to_google_sheet');

function net_save_order_data_to_google_sheet($order_id)
{
    if (!$order_id) {
        return;
    }

    // Get the order object
    $order = wc_get_order($order_id);

    if (!$order) {
        return;
    }


    // Custom billing fields
    $custom_fields = [
        '_billing_father_name',
        '_billing_name',
        '_billing_cnic',
        '_billing_age',
        '_billing_contact',
        '_billing_emergency_contact',
        '_billing_slots',
        '_billing_sharing_type',
        '_billing_package_type',
        '_billing_pickup_location',
    ];
    $customer_data = [];
    foreach ($custom_fields as $field) {
        $customer_data[$field] = $order->get_meta($field, true);
    }

    $tour_data = [];
    foreach ($order->get_items() as $item_id => $item) {
        // Additional metadata
        $tour_id = $item->get_meta('_st_st_booking_id', true);
        $tour_data['tour_id'] = $tour_id;
        $tour_data['tour_name'] = $item->get_name();
        $tour_data['tour_duration'] = $item->get_meta('_st_duration', true);
        $tour_data['tour_adult_number'] = $item->get_meta('_st_adult_number', true);
        $tour_data['tour_child_number'] = $item->get_meta('_st_child_number', true);
        $tour_data['tour_date'] = $item->get_meta('_st_check_in', true);

        if ($tour_id) {
            $slug = get_post_field('post_name', $tour_id);
            $url = site_url('/tour/' . $slug);
            $tour_data['tour_url'] = $url;
        }
    }

    $order_data = [
        'date' => $order->get_date_created()->date('Y-m-d'),
        'time' => $order->get_date_created()->date('H:i:s'),
        'order_id' => $order->get_id(),
        'order_total' => $order->get_total(),
        'payment_method' => $order->get_payment_method_title(),
        'order_link' => admin_url('post.php?post=' . $order_id . '&action=edit'),
        'customer_name' => $customer_data['_billing_name'],
        'customer_email' => $order->get_billing_email(),
        'customer_phone' => $customer_data['_billing_contact'],
        'customer_emergency_contact' => $customer_data['_billing_emergency_contact'],
        'customer_age' => $customer_data['_billing_age'],
        'customer_cnic' => $customer_data['_billing_cnic'],
        'customer_father_name' => $customer_data['_billing_father_name'],
        'customer_slots' => $customer_data['_billing_slots'],
        'customer_pickup_location' => ucfirst($customer_data['_billing_pickup_location']),
        'customer_sharing_type' => ucfirst($customer_data['_billing_sharing_type']),
        'customer_package_type' => $customer_data['_billing_package_type'],
        'tour_id' => $tour_data['tour_id'],
        'tour_name' => $tour_data['tour_name'],
        'tour_duration' => $tour_data['tour_duration'],
        'tour_date' => $tour_data['tour_date'],
        'tour_adult_number' => $tour_data['tour_adult_number'],
        'tour_child_number' => $tour_data['tour_child_number'],
        'tour_url' => $tour_data['tour_url']
    ];

    $data_to_sheet = [
        $order_data['date'],
        $order_data['time'],
        $order_data['order_id'],
        $order_data['tour_name'],
        $order_data['customer_name'],
        $order_data['customer_phone'],
        $order_data['customer_slots'],
        $order_data['tour_adult_number'],
        $order_data['tour_child_number'],
        $order_data['customer_pickup_location'],
        $order_data['tour_date'],
        $order_data['customer_sharing_type'],
        $order_data['customer_package_type'],
        $order_data['order_total'],
        $order_data['customer_emergency_contact'],
        $order_data['customer_father_name'],
        $order_data['customer_cnic'],
        $order_data['customer_age'],
        $order_data['tour_id'],
        $order_data['customer_email'],
        $order_data['tour_duration'],
        $order_data['payment_method'],
        $order_data['tour_url'],
        $order_data['order_link']
    ];

    $spreadsheetId = '11ScdSgzky5jco0S0xsQPcno0v0Ug-8AYIYriFO9TioA';
    $worksheetName = 'Sheet1';
    net_append_to_google_sheet($spreadsheetId, $worksheetName, $data_to_sheet);
}


function net_services_inquiry_form_handler($record)
{
    $data_to_sheet = [
        date('F j, Y'),
        date('g:i A'),
        $record['name_customer'],
        $record['email_customer'],
        $record['phone_customer'],
        $record['customer_note'],
        $record['type_service'],
        $record['name_service']
    ];

    $spreadsheetId = '1ZRLZR-agZFWXrtQDNhmC0nzByuk-wlNWe93EtIQ1Ipk';
    $worksheetName = 'Sheet1';

    net_append_to_google_sheet($spreadsheetId, $worksheetName, $data_to_sheet);
}
add_action('init', function () {

    $spreadsheetId = '11ScdSgzky5jco0S0xsQPcno0v0Ug-8AYIYriFO9TioA';
    $worksheetName = 'Sheet1';
    $data_to_sheet =
        [
            'ABC',
            'DEF',
            'GHI'

        ];
    net_append_to_google_sheet($spreadsheetId, $worksheetName, $data_to_sheet);
});
