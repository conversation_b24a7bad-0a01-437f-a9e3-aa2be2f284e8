.about {
  padding: 6rem 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.about-description {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #555;
  margin-bottom: 1.5rem;
}

.skills-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1.5rem;
}

.skill-card {
  background: white;
  padding: 2rem 1rem;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
}

.skill-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--skill-color);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.skill-card:hover::before {
  transform: scaleX(1);
}

.skill-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-color: var(--skill-color);
}

.skill-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.skill-name {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .skills-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
  }
  
  .skill-card {
    padding: 1.5rem 0.5rem;
  }
  
  .skill-icon {
    font-size: 2rem;
  }
}
