{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SMILE ONE\\\\react-portfolio\\\\src\\\\components\\\\About.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport './About.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  const [skills, setSkills] = useState([\n  // Initial skills state with static data. This can be replaced with API data or other dynamic data sources.\n  {\n    name: 'React',\n    icon: '⚛️',\n    color: '#61DAFB'\n  }, {\n    name: 'TypeScript',\n    icon: '📘',\n    color: '#3178C6'\n  }, {\n    name: 'Node.js',\n    icon: '🟢',\n    color: '#339933'\n  }, {\n    name: 'Python',\n    icon: '🐍',\n    color: '#3776AB'\n  }, {\n    name: 'Design',\n    icon: '🎨',\n    color: '#FF6B6B'\n  }, {\n    name: 'Database',\n    icon: '🗄️',\n    color: '#4DB33D'\n  }]); // Initial skills state with static data. This can be replaced with API data or other dynamic data sources.\n\n  // Get skills from skills.json api\n  useEffect(() => {\n    // Fetch skills from API on component mount. This can be replaced with other data sources or logic.\n    fetch('http://localhost:3000/api/skills').then(response => response.json()).then(data => {\n      setSkills(data);\n    }).catch(error => {\n      console.error('Error fetching skills:', error);\n      // Keep the default skills if API fails\n    });\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"about\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"containeer\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"My Name \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Passionate about creating amazing digital experiences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"about-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"about-text\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"about-description\",\n            children: \"I'm a full-stack developer with a passion for creating beautiful, functional, and user-friendly applications. With expertise in modern web technologies, I bring ideas to life through clean code and innovative design.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"about-description\",\n            children: \"When I'm not coding, you can find me exploring new technologies, contributing to open-source projects, or sharing knowledge with the developer community.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 48,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"skills-grid\",\n          children: skills.map((skill, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"skill-card\",\n            style: {\n              '--skill-color': skill.color\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"skill-icon\",\n              children: skill.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"skill-name\",\n              children: skill.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this)]\n          }, skill.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"we1aX/0XEcrw+zwAtYjQ0OAvaSo=\");\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "jsxDEV", "_jsxDEV", "About", "_s", "skills", "setSkills", "name", "icon", "color", "fetch", "then", "response", "json", "data", "catch", "error", "console", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "skill", "index", "style", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SMILE ONE/react-portfolio/src/components/About.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport './About.css';\r\n\r\ninterface Skill {\r\n  name: string;\r\n  icon: string;\r\n  color: string;\r\n}\r\n\r\nconst About: React.FC = () => {\r\n  const [skills, setSkills] = useState<Skill[]>([  // Initial skills state with static data. This can be replaced with API data or other dynamic data sources.\r\n    { name: 'React', icon: '⚛️', color: '#61DAFB' },\r\n    { name: 'TypeScript', icon: '📘', color: '#3178C6' },\r\n    { name: 'Node.js', icon: '🟢', color: '#339933' },\r\n    { name: 'Python', icon: '🐍', color: '#3776AB' },\r\n    { name: 'Design', icon: '🎨', color: '#FF6B6B' },\r\n    { name: 'Database', icon: '🗄️', color: '#4DB33D' },\r\n  ]);  // Initial skills state with static data. This can be replaced with API data or other dynamic data sources.\r\n\r\n  // Get skills from skills.json api\r\n  useEffect(() => {  // Fetch skills from API on component mount. This can be replaced with other data sources or logic.\r\n    fetch('http://localhost:3000/api/skills')\r\n      .then(response => response.json())\r\n      .then(data => {\r\n        setSkills(data);\r\n      })\r\n      .catch(error => {\r\n        console.error('Error fetching skills:', error);\r\n        // Keep the default skills if API fails\r\n      });\r\n  }, []);\r\n\r\n  return (\r\n    <section id=\"about\" className=\"about\">\r\n      <div className=\"containeer\">\r\n        <div className=\"section-header\">\r\n          <h2 className=\"section-title\">My Name </h2>\r\n          <p className=\"section-subtitle\">Passionate about creating amazing digital experiences</p>\r\n        </div>\r\n        \r\n        <div className=\"about-content\">\r\n          <div className=\"about-text\">\r\n            <p className=\"about-description\">\r\n              I'm a full-stack developer with a passion for creating beautiful, functional, \r\n              and user-friendly applications. With expertise in modern web technologies, \r\n              I bring ideas to life through clean code and innovative design.\r\n            </p>\r\n            <p className=\"about-description\">\r\n              When I'm not coding, you can find me exploring new technologies, \r\n              contributing to open-source projects, or sharing knowledge with the developer community.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"skills-grid\">\r\n            {skills.map((skill, index) => (\r\n              <div \r\n                key={skill.name} \r\n                className=\"skill-card\"\r\n                style={{ '--skill-color': skill.color } as React.CSSProperties}\r\n              >\r\n                <div className=\"skill-icon\">{skill.icon}</div>\r\n                <span className=\"skill-name\">{skill.name}</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default About;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQrB,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGN,QAAQ,CAAU;EAAG;EAC/C;IAAEO,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAEF,IAAI,EAAE,YAAY;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EACpD;IAAEF,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EAChD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,IAAI,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAU,CAAC,EAChD;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,CACpD,CAAC,CAAC,CAAE;;EAEL;EACAV,SAAS,CAAC,MAAM;IAAG;IACjBW,KAAK,CAAC,kCAAkC,CAAC,CACtCC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC,CAAC,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAI;MACZR,SAAS,CAACQ,IAAI,CAAC;IACjB,CAAC,CAAC,CACDC,KAAK,CAACC,KAAK,IAAI;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C;IACF,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEd,OAAA;IAASgB,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,OAAO;IAAAC,QAAA,eACnClB,OAAA;MAAKiB,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBlB,OAAA;QAAKiB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BlB,OAAA;UAAIiB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3CtB,OAAA;UAAGiB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAAqD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtF,CAAC,eAENtB,OAAA;QAAKiB,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BlB,OAAA;UAAKiB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBlB,OAAA;YAAGiB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAIjC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAGiB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAGjC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENtB,OAAA;UAAKiB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBf,MAAM,CAACoB,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBzB,OAAA;YAEEiB,SAAS,EAAC,YAAY;YACtBS,KAAK,EAAE;cAAE,eAAe,EAAEF,KAAK,CAACjB;YAAM,CAAyB;YAAAW,QAAA,gBAE/DlB,OAAA;cAAKiB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEM,KAAK,CAAClB;YAAI;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9CtB,OAAA;cAAMiB,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAEM,KAAK,CAACnB;YAAI;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GAL3CE,KAAK,CAACnB,IAAI;YAAAc,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMZ,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACpB,EAAA,CA5DID,KAAe;AAAA0B,EAAA,GAAf1B,KAAe;AA8DrB,eAAeA,KAAK;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}