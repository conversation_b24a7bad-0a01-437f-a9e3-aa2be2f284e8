<?php
class Smile_One_Orders {
    private static $instance;

    public static function init() {
        if (!isset(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // Add custom fields to checkout
        add_action('woocommerce_before_order_notes', array($this, 'add_checkout_fields'));
        add_action('woocommerce_checkout_process', array($this, 'validate_checkout_fields'));
        add_action('woocommerce_checkout_update_order_meta', array($this, 'save_checkout_fields'));
        
        // Process order after payment
        add_action('woocommerce_payment_complete', array($this, 'process_smile_order'));
        add_action('woocommerce_order_status_completed', array($this, 'process_smile_order'));
        
        // Add admin order meta box
        add_action('add_meta_boxes', array($this, 'add_order_meta_box'));
    }

    public function add_checkout_fields($checkout) {
        global $woocommerce;
        
        foreach ($woocommerce->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            
            if ($product->get_meta('_smile_product_id') || $this->has_smile_variation($product)) {
                $game_id_label = $product->get_meta('_smile_game_id') ?: 'Player ID';
                $require_zone = $product->get_meta('_smile_require_zone') === 'yes';
                
                echo '<div id="smile_one_fields"><h3>' . __('Game Account Information', 'smile-one-wc') . '</h3>';
                
                woocommerce_form_field('smile_player_id', array(
                    'type' => 'text',
                    'class' => array('form-row-wide'),
                    'label' => __($game_id_label, 'smile-one-wc'),
                    'required' => true,
                ), $checkout->get_value('smile_player_id'));
                
                if ($require_zone) {
                    woocommerce_form_field('smile_zone_id', array(
                        'type' => 'text',
                        'class' => array('form-row-wide'),
                        'label' => __('Zone/Server ID', 'smile-one-wc'),
                        'required' => true,
                    ), $checkout->get_value('smile_zone_id'));
                }
                
                echo '</div>';
                break; // Only show once since all smile products need same info
            }
        }
    }

    private function has_smile_variation($product) {
        if ($product->is_type('variable')) {
            foreach ($product->get_available_variations() as $variation) {
                $variation_obj = wc_get_product($variation['variation_id']);
                if ($variation_obj->get_meta('_smile_variation_product_id')) {
                    return true;
                }
            }
        }
        return false;
    }

    public function validate_checkout_fields() {
        global $woocommerce;
        
        foreach ($woocommerce->cart->get_cart() as $cart_item) {
            $product = $cart_item['data'];
            
            if ($product->get_meta('_smile_product_id') || $this->has_smile_variation($product)) {
                if (empty($_POST['smile_player_id'])) {
                    wc_add_notice(__('Please enter your Player ID.', 'smile-one-wc'), 'error');
                }
                
                if ($product->get_meta('_smile_require_zone') === 'yes' && empty($_POST['smile_zone_id'])) {
                    wc_add_notice(__('Please enter your Zone/Server ID.', 'smile-one-wc'), 'error');
                }
                break;
            }
        }
    }

    public function save_checkout_fields($order_id) {
        if (!empty($_POST['smile_player_id'])) {
            update_post_meta($order_id, '_smile_player_id', sanitize_text_field($_POST['smile_player_id']));
        }
        
        if (!empty($_POST['smile_zone_id'])) {
            update_post_meta($order_id, '_smile_zone_id', sanitize_text_field($_POST['smile_zone_id']));
        }
    }

    public function process_smile_order($order_id) {
        $order = wc_get_order($order_id);
        
        // Check if order already processed
        if (get_post_meta($order_id, '_smile_order_processed', true)) {
            return;
        }
        
        $api = Smile_One_API::init();
        $items = $order->get_items();
        
        foreach ($items as $item) {
            $product = $item->get_product();
            $smile_product_id = $product->get_meta('_smile_product_id');
            
            // Check for variation product ID
            if (!$smile_product_id && $product->is_type('variation')) {
                $smile_product_id = $product->get_meta('_smile_variation_product_id');
            }
            
            if ($smile_product_id) {
                $player_id = get_post_meta($order_id, '_smile_player_id', true);
                $zone_id = get_post_meta($order_id, '_smile_zone_id', true) ?: $player_id;
                
                $params = array(
                    'uid' => $api->settings['uid'],
                    'email' => $api->settings['email'],
                    'product' => 'mobilelegends',
                    'userid' => $player_id,
                    'zoneid' => $zone_id,
                    'productid' => $smile_product_id
                );
                
                $response = $api->make_api_request('/smilecoin/api/createorder', $params);
                
                if ($response['status'] == 200) {
                    // Mark as processed
                    update_post_meta($order_id, '_smile_order_processed', 'yes');
                    update_post_meta($order_id, '_smile_order_id', $response['order_id']);
                    
                    // Save to history table
                    global $wpdb;
                    $table_name = $wpdb->prefix . 'smile_one_orders';
                    
                    $wpdb->insert($table_name, array(
                        'wc_order_id' => $order_id,
                        'smile_order_id' => $response['order_id'],
                        'user_id' => $order->get_customer_id(),
                        'product_id' => $product->get_id(),
                        'status' => 'processing'
                    ));
                    
                    // Add order note
                    $order->add_order_note(sprintf(
                        __('Smile One order created. Order ID: %s', 'smile-one-wc'),
                        $response['order_id']
                    ));
                } else {
                    $order->add_order_note(sprintf(
                        __('Smile One order failed. Error: %s', 'smile-one-wc'),
                        $response['message'] ?? 'Unknown error'
                    ));
                }
            }
        }
    }

    public function add_order_meta_box() {
        add_meta_box(
            'smile_one_order_details',
            __('Smile One Order Details', 'smile-one-wc'),
            array($this, 'render_order_meta_box'),
            'shop_order',
            'side',
            'default'
        );
    }

    public function render_order_meta_box($post) {
        $order = wc_get_order($post->ID);
        $smile_order_id = get_post_meta($post->ID, '_smile_order_id', true);
        $player_id = get_post_meta($post->ID, '_smile_player_id', true);
        $zone_id = get_post_meta($post->ID, '_smile_zone_id', true);
        
        if ($smile_order_id) {
            echo '<p><strong>' . __('Smile One Order ID:', 'smile-one-wc') . '</strong> ' . esc_html($smile_order_id) . '</p>';
            echo '<p><strong>' . __('Player ID:', 'smile-one-wc') . '</strong> ' . esc_html($player_id) . '</p>';
            
            if ($zone_id && $zone_id != $player_id) {
                echo '<p><strong>' . __('Zone/Server ID:', 'smile-one-wc') . '</strong> ' . esc_html($zone_id) . '</p>';
            }
            
            echo '<button id="check-smile-status" class="button button-secondary">' . __('Check Status', 'smile-one-wc') . '</button>';
            echo '<div id="smile-status-result" style="margin-top: 10px;"></div>';
            
            ?>
            <script>
            jQuery(document).ready(function($) {
                $('#check-smile-status').click(function() {
                    $('#smile-status-result').html('<p>Checking status...</p>');
                    
                    $.post(ajaxurl, {
                        action: 'check_smile_order_status',
                        order_id: '<?php echo $post->ID; ?>',
                        security: '<?php echo wp_create_nonce("smile_order_status"); ?>'
                    }, function(response) {
                        if (response.success) {
                            $('#smile-status-result').html('<div class="notice notice-success"><p>Status: ' + response.data.status + '</p></div>');
                        } else {
                            $('#smile-status-result').html('<div class="notice notice-error"><p>Error: ' + response.data.message + '</p></div>');
                        }
                    });
                });
            });
            </script>
            <?php
        } else {
            echo '<p>' . __('This order has not been processed with Smile One yet.', 'smile-one-wc') . '</p>';
        }
    }
}