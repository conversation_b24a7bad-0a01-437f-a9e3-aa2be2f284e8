[{"C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\reportWebVitals.ts": "2", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\App.tsx": "3", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Portfolio.tsx": "4", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Hero.tsx": "5", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Header.tsx": "6", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\About.tsx": "7", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Projects.tsx": "8", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Contact.tsx": "9", "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Educattion.tsx": "10"}, {"size": 630, "mtime": 1751735855435, "results": "11", "hashOfConfig": "12"}, {"size": 425, "mtime": 1751735936862, "results": "13", "hashOfConfig": "12"}, {"size": 254, "mtime": 1751735498829, "results": "14", "hashOfConfig": "12"}, {"size": 763, "mtime": 1751783726612, "results": "15", "hashOfConfig": "12"}, {"size": 1211, "mtime": 1751729991971, "results": "16", "hashOfConfig": "12"}, {"size": 1308, "mtime": 1751729987122, "results": "17", "hashOfConfig": "12"}, {"size": 2835, "mtime": 1751783510598, "results": "18", "hashOfConfig": "12"}, {"size": 3070, "mtime": 1751730006623, "results": "19", "hashOfConfig": "12"}, {"size": 3580, "mtime": 1751730021765, "results": "20", "hashOfConfig": "12"}, {"size": 1667, "mtime": 1751783622599, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "5n193e", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Portfolio.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\About.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Projects.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Contact.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\SMILE ONE\\react-portfolio\\src\\components\\Educattion.tsx", [], []]