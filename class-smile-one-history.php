<?php
class Smile_One_History {
    private static $instance;

    public static function init() {
        if (!isset(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // Add menu item
        add_action('admin_menu', array($this, 'add_history_page'));
        
        // Add shortcode for frontend history
        add_shortcode('smile_one_order_history', array($this, 'render_frontend_history'));
        
        // AJAX handler for status check
        add_action('wp_ajax_check_smile_order_status', array($this, 'check_order_status'));
    }

    public function add_history_page() {
        add_submenu_page(
            'woocommerce',
            'Smile One Orders',
            'Smile One Orders',
            'manage_options',
            'smile-one-orders',
            array($this, 'render_history_page')
        );
    }

    public function render_history_page() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'smile_one_orders';
        
        $orders = $wpdb->get_results("
            SELECT * FROM $table_name
            ORDER BY created_at DESC
            LIMIT 100
        ");
        
        ?>
        <div class="wrap">
            <h1>Smile One Order History</h1>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Customer</th>
                        <th>Product</th>
                        <th>Smile Order ID</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order) : 
                        $wc_order = wc_get_order($order->wc_order_id);
                        $product = wc_get_product($order->product_id);
                        $user = get_user_by('id', $order->user_id);
                    ?>
                        <tr>
                            <td>
                                <a href="<?php echo get_edit_post_link($order->wc_order_id); ?>">
                                    #<?php echo $order->wc_order_id; ?>
                                </a>
                            </td>
                            <td>
                                <?php if ($user) : ?>
                                    <a href="<?php echo get_edit_user_link($order->user_id); ?>">
                                        <?php echo esc_html($user->display_name); ?>
                                    </a>
                                <?php endif; ?>
                            </td>
                            <td><?php echo $product ? esc_html($product->get_name()) : 'Product deleted'; ?></td>
                            <td><?php echo esc_html($order->smile_order_id); ?></td>
                            <td><?php echo esc_html($order->status); ?></td>
                            <td><?php echo date_i18n(get_option('date_format'), strtotime($order->created_at)); ?></td>
                            <td>
                                <button class="button button-small check-status" data-order-id="<?php echo $order->wc_order_id; ?>">
                                    Check Status
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <script>
            jQuery(document).ready(function($) {
                $('.check-status').click(function() {
                    var button = $(this);
                    button.text('Checking...').prop('disabled', true);
                    
                    $.post(ajaxurl, {
                        action: 'check_smile_order_status',
                        order_id: button.data('order-id'),
                        security: '<?php echo wp_create_nonce("smile_order_status"); ?>'
                    }, function(response) {
                        if (response.success) {
                            alert('Current status: ' + response.data.status);
                        } else {
                            alert('Error: ' + response.data.message);
                        }
                        button.text('Check Status').prop('disabled', false);
                    });
                });
            });
            </script>
        </div>
        <?php
    }

    public function render_frontend_history() {
        if (!is_user_logged_in()) {
            return '<p>Please log in to view your order history.</p>';
        }
        
        global $wpdb;
        $table_name = $wpdb->prefix . 'smile_one_orders';
        $user_id = get_current_user_id();
        
        $orders = $wpdb->get_results($wpdb->prepare("
            SELECT * FROM $table_name
            WHERE user_id = %d
            ORDER BY created_at DESC
        ", $user_id));
        
        ob_start();
        ?>
        <div class="smile-one-history">
            <h2>Your Smile One Orders</h2>
            
            <?php if (empty($orders)) : ?>
                <p>You haven't placed any Smile One orders yet.</p>
            <?php else : ?>
                <table class="shop_table shop_table_responsive">
                    <thead>
                        <tr>
                            <th>Order</th>
                            <th>Product</th>
                            <th>Smile Order ID</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($orders as $order) : 
                            $wc_order = wc_get_order($order->wc_order_id);
                            $product = wc_get_product($order->product_id);
                        ?>
                            <tr>
                                <td>
                                    <a href="<?php echo $wc_order ? $wc_order->get_view_order_url() : '#'; ?>">
                                        #<?php echo $order->wc_order_id; ?>
                                    </a>
                                </td>
                                <td><?php echo $product ? esc_html($product->get_name()) : 'Product deleted'; ?></td>
                                <td><?php echo esc_html($order->smile_order_id); ?></td>
                                <td><?php echo esc_html($order->status); ?></td>
                                <td><?php echo date_i18n(get_option('date_format'), strtotime($order->created_at)); ?></td>
                                <td>
                                    <button class="button check-status" data-order-id="<?php echo $order->wc_order_id; ?>">
                                        Check Status
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            $('.check-status').click(function() {
                var button = $(this);
                button.text('Checking...').prop('disabled', true);
                
                $.post('<?php echo admin_url("admin-ajax.php"); ?>', {
                    action: 'check_smile_order_status',
                    order_id: button.data('order-id'),
                    security: '<?php echo wp_create_nonce("smile_order_status"); ?>'
                }, function(response) {
                    if (response.success) {
                        alert('Current status: ' + response.data.status);
                    } else {
                        alert('Error: ' + response.data.message);
                    }
                    button.text('Check Status').prop('disabled', false);
                });
            });
        });
        </script>
        <?php
        return ob_get_clean();
    }

    public function check_order_status() {
        check_ajax_referer('smile_order_status', 'security');
        
        $order_id = intval($_POST['order_id']);
        $smile_order_id = get_post_meta($order_id, '_smile_order_id', true);
        
        if (!$smile_order_id) {
            wp_send_json_error(array(
                'message' => 'No Smile One order ID found'
            ));
        }
        
        $api = Smile_One_API::init();
        $params = array(
            'uid' => $api->settings['uid'],
            'email' => $api->settings['email'],
            'product' => 'mobilelegends',
            'order_id' => $smile_order_id
        );
        
        // Note: You'll need to implement a status check endpoint with Smile One
        // This is a placeholder as the documentation doesn't show a status check endpoint
        $response = $api->make_api_request('/smilecoin/api/orderstatus', $params);
        
        if ($response['status'] == 200) {
            // Update status in database
            global $wpdb;
            $table_name = $wpdb->prefix . 'smile_one_orders';
            
            $wpdb->update($table_name, array(
                'status' => sanitize_text_field($response['status']),
                'updated_at' => current_time('mysql')
            ), array(
                'wc_order_id' => $order_id
            ));
            
            wp_send_json_success(array(
                'status' => $response['status']
            ));
        } else {
            wp_send_json_error(array(
                'message' => $response['message'] ?? 'Failed to check status'
            ));
        }
    }
}