<?php
/**
 * Plugin Name: Smile One WooCommerce Integration
 * Description: Connect WooCommerce to Smile One API for game currency purchases
 * Version: 1.0
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>
 */

defined('ABSPATH') or die('Direct access not allowed');

// Check if WooCommerce is active
if (!in_array('woocommerce/woocommerce.php', apply_filters('active_plugins', get_option('active_plugins')))) {
    add_action('admin_notices', function() {
        echo '<div class="error"><p>Smile One WooCommerce Integration requires WooCommerce to be installed and active.</p></div>';
    });
    return;
}

// Define plugin constants
define('SMILE_ONE_WC_PATH', plugin_dir_path(__FILE__));
define('SMILE_ONE_WC_URL', plugin_dir_url(__FILE__));

// Include required files
require_once SMILE_ONE_WC_PATH . 'class-smile-one-api.php';
require_once SMILE_ONE_WC_PATH . 'class-smile-one-products.php';
require_once SMILE_ONE_WC_PATH . 'class-smile-one-orders.php';
require_once SMILE_ONE_WC_PATH . 'class-smile-one-history.php';

// Initialize classes
Smile_One_API::init();
Smile_One_Products::init();
Smile_One_Orders::init();
Smile_One_History::init();

// Activation hook
register_activation_hook(__FILE__, 'smile_one_wc_activate');
function smile_one_wc_activate() {
    // Set default options
    if (!get_option('smile_one_wc_settings')) {
        $defaults = array(
            'email' => '',
            'uid' => '',
            'secret_key' => '',
            'country' => 'ph'
        );
        update_option('smile_one_wc_settings', $defaults);
    }
    
    // Create database table for order history
    global $wpdb;
    $table_name = $wpdb->prefix . 'smile_one_orders';
    
    $charset_collate = $wpdb->get_charset_collate();
    
    $sql = "CREATE TABLE $table_name (
        id mediumint(9) NOT NULL AUTO_INCREMENT,
        wc_order_id bigint(20) NOT NULL,
        smile_order_id varchar(100) NOT NULL,
        user_id bigint(20) NOT NULL,
        product_id bigint(20) NOT NULL,
        status varchar(50) DEFAULT 'pending',
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
}