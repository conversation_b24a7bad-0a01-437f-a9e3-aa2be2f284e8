{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SMILE ONE\\\\react-portfolio\\\\src\\\\components\\\\Portfolio.tsx\";\nimport React from 'react';\nimport Header from './Header';\nimport Hero from './Hero';\nimport About from './About';\nimport Projects from './Projects';\nimport Education from './Educattion';\nimport Contact from './Contact';\nimport './Portfolio.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Portfolio = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"portfolio\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animated-background\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"gradient-orb orb-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(Hero, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(About, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Projects, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Contact, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Education, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Portfolio;\nexport default Portfolio;\nvar _c;\n$RefreshReg$(_c, \"Portfolio\");", "map": {"version": 3, "names": ["React", "Header", "Hero", "About", "Projects", "Education", "Contact", "jsxDEV", "_jsxDEV", "Portfolio", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SMILE ONE/react-portfolio/src/components/Portfolio.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Header from './Header';\r\nimport Hero from './Hero';\r\nimport About from './About';\r\nimport Projects from './Projects';\r\nimport Education from './Educattion';\r\nimport Contact from './Contact';\r\nimport './Portfolio.css';\r\n\r\nconst Portfolio: React.FC = () => {\r\n  return (\r\n    <div className=\"portfolio\">\r\n      <div className=\"animated-background\">\r\n        <div className=\"gradient-orb orb-1\"></div>\r\n        <div className=\"gradient-orb orb-2\"></div>\r\n        <div className=\"gradient-orb orb-3\"></div>\r\n      </div>\r\n      \r\n      <Header />\r\n      <main>\r\n        <Hero />\r\n        <About />\r\n        <Projects />\r\n        <Contact />\r\n        <Education /> \r\n      </main>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Portfolio;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAO,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACED,OAAA;IAAKE,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBH,OAAA;MAAKE,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCH,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CP,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC1CP,OAAA;QAAKE,SAAS,EAAC;MAAoB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvC,CAAC,eAENP,OAAA,CAACP,MAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACVP,OAAA;MAAAG,QAAA,gBACEH,OAAA,CAACN,IAAI;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACRP,OAAA,CAACL,KAAK;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACTP,OAAA,CAACJ,QAAQ;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACZP,OAAA,CAACF,OAAO;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXP,OAAA,CAACH,SAAS;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACC,EAAA,GAnBIP,SAAmB;AAqBzB,eAAeA,SAAS;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}