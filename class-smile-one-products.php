<?php
class Smile_One_Products {
    private static $instance;

    public static function init() {
        if (!isset(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // Add custom product type
        add_filter('product_type_selector', array($this, 'add_smile_product_type'));
        add_action('admin_footer', array($this, 'smile_product_custom_js'));
        add_action('woocommerce_product_options_general_product_data', array($this, 'smile_product_custom_fields'));
        add_action('woocommerce_process_product_meta', array($this, 'save_smile_product_fields'));
        
        // Add variation fields
        add_action('woocommerce_product_after_variable_attributes', array($this, 'smile_variation_fields'), 10, 3);
        add_action('woocommerce_save_product_variation', array($this, 'save_smile_variation_fields'), 10, 2);
    }

    public function add_smile_product_type($types) {
        $types['smile_product'] = __('Smile One Product', 'smile-one-wc');
        return $types;
    }

    public function smile_product_custom_js() {
        if ('product' != get_post_type()) return;
        
        ?>
        <script>
        jQuery(document).ready(function($) {
            // Show/hide fields based on product type
            $('.options_group.pricing').addClass('show_if_smile_product');
            
            // Hide type specific options
            $('.show_if_simple').addClass('show_if_smile_product');
            $('.show_if_variable').addClass('show_if_smile_product');
            
            // Change "Simple product" to "Game Currency" in dropdown
            $('select#product-type option[value="simple"]').text('Game Currency (Simple)');
            $('select#product-type option[value="variable"]').text('Game Currency (Variable)');
        });
        </script>
        <?php
    }

    public function smile_product_custom_fields() {
        global $product_object;
        
        echo '<div class="options_group show_if_smile_product">';
        
        // Smile One Product ID
        woocommerce_wp_text_input(array(
            'id' => '_smile_product_id',
            'label' => __('Smile One Product ID', 'smile-one-wc'),
            'description' => __('The product ID from Smile One API', 'smile-one-wc'),
            'desc_tip' => true,
            'type' => 'text'
        ));
        
        // Game ID Field
        woocommerce_wp_text_input(array(
            'id' => '_smile_game_id',
            'label' => __('Player ID Field Label', 'smile-one-wc'),
            'description' => __('Label for the player ID field (e.g., "Player ID", "IGN", "Account ID")', 'smile-one-wc'),
            'desc_tip' => true,
            'type' => 'text',
            'value' => get_post_meta($product_object->get_id(), '_smile_game_id', true) ?: 'Player ID'
        ));
        
        // Zone ID Field (optional)
        woocommerce_wp_checkbox(array(
            'id' => '_smile_require_zone',
            'label' => __('Require Zone/Server', 'smile-one-wc'),
            'description' => __('Check if this product requires a zone/server selection', 'smile-one-wc'),
            'desc_tip' => true
        ));
        
        echo '</div>';
    }

    public function save_smile_product_fields($post_id) {
        $product = wc_get_product($post_id);
        
        $smile_product_id = isset($_POST['_smile_product_id']) ? sanitize_text_field($_POST['_smile_product_id']) : '';
        $smile_game_id = isset($_POST['_smile_game_id']) ? sanitize_text_field($_POST['_smile_game_id']) : '';
        $require_zone = isset($_POST['_smile_require_zone']) ? 'yes' : 'no';
        
        $product->update_meta_data('_smile_product_id', $smile_product_id);
        $product->update_meta_data('_smile_game_id', $smile_game_id);
        $product->update_meta_data('_smile_require_zone', $require_zone);
        $product->save();
    }

    public function smile_variation_fields($loop, $variation_data, $variation) {
        $variation_obj = wc_get_product($variation->ID);
        $smile_product_id = $variation_obj->get_meta('_smile_variation_product_id', true);
        
        ?>
        <div class="form-row form-row-full">
            <p class="form-field _smile_variation_product_id_field">
                <label for="_smile_variation_product_id_<?php echo $loop; ?>">
                    <?php _e('Smile One Product ID', 'smile-one-wc'); ?>
                </label>
                <input
                    type="text"
                    id="_smile_variation_product_id_<?php echo $loop; ?>"
                    name="_smile_variation_product_id[<?php echo $loop; ?>]"
                    value="<?php echo esc_attr($smile_product_id); ?>"
                    placeholder="<?php _e('Enter Smile One Product ID', 'smile-one-wc'); ?>"
                >
            </p>
        </div>
        <?php
    }

    public function save_smile_variation_fields($variation_id, $loop) {
        $smile_product_id = isset($_POST['_smile_variation_product_id'][$loop]) ? sanitize_text_field($_POST['_smile_variation_product_id'][$loop]) : '';
        
        $variation = wc_get_product($variation_id);
        $variation->update_meta_data('_smile_variation_product_id', $smile_product_id);
        $variation->save();
    }
}