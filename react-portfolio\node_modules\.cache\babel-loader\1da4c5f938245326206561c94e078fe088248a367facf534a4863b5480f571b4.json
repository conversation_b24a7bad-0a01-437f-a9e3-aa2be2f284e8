{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\SMILE ONE\\\\react-portfolio\\\\src\\\\components\\\\Educattion.tsx\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n/**\r\n * Education component for the portfolio.\r\n */\nconst Education = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"education\",\n    className: \"education\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Education\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-subtitle\",\n          children: \"Where I've learned and grown\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 10,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"education-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"education-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"education-title\",\n            children: \"Bachelor of Science in Computer Science\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"education-institution\",\n            children: \"University of Example\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"education-date\",\n            children: \"Graduated: May 2020\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"education-description\",\n            children: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, mi sit amet tincidunt bibendum, nunc nisl tincidunt nisl, nec bibendum nunc nisl sit amet nunc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"education-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"education-title\",\n            children: \"Certified Web Developer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"education-institution\",\n            children: \"Codecademy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"education-date\",\n            children: \"Completed: August 2018\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"education-description\",\n            children: \"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, mi sit amet tincidunt bibendum, nunc nisl tincidunt nisl, nec bibendum nunc nisl sit amet nunc.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Education;\nexport default Education;\nvar _c;\n$RefreshReg$(_c, \"Education\");", "map": {"version": 3, "names": ["Education", "_jsxDEV", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/SMILE ONE/react-portfolio/src/components/Educattion.tsx"], "sourcesContent": ["/**\r\n * Education component for the portfolio.\r\n */\r\nconst Education: React.FC = () => {\r\n  return (\r\n    <section id=\"education\" className=\"education\">\r\n      <div className=\"container\">\r\n        <div className=\"section-header\">\r\n          <h2 className=\"section-title\">Education</h2>\r\n          <p className=\"section-subtitle\">Where I've learned and grown</p>\r\n        </div>\r\n        \r\n        <div className=\"education-content\">\r\n          <div className=\"education-item\">\r\n            <h3 className=\"education-title\">Bachelor of Science in Computer Science</h3>\r\n            <p className=\"education-institution\">University of Example</p>\r\n            <p className=\"education-date\">Graduated: May 2020</p>\r\n            <p className=\"education-description\">\r\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit. \r\n              Nullam euismod, mi sit amet tincidunt bibendum, nunc nisl \r\n              tincidunt nisl, nec bibendum nunc nisl sit amet nunc.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"education-item\">\r\n            <h3 className=\"education-title\">Certified Web Developer</h3>\r\n            <p className=\"education-institution\">Codecademy</p>\r\n            <p className=\"education-date\">Completed: August 2018</p>\r\n            <p className=\"education-description\">\r\n              Lorem ipsum dolor sit amet, consectetur adipiscing elit. \r\n              Nullam euismod, mi sit amet tincidunt bibendum, nunc nisl \r\n              tincidunt nisl, nec bibendum nunc nisl sit amet nunc.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Education;\r\n"], "mappings": ";;AAAA;AACA;AACA;AACA,MAAMA,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACEC,OAAA;IAASC,EAAE,EAAC,WAAW;IAACC,SAAS,EAAC,WAAW;IAAAC,QAAA,eAC3CH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBH,OAAA;QAAKE,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BH,OAAA;UAAIE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CP,OAAA;UAAGE,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7D,CAAC,eAENP,OAAA;QAAKE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCH,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAIE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAuC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EP,OAAA;YAAGE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC9DP,OAAA;YAAGE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrDP,OAAA;YAAGE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAIrC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAENP,OAAA;UAAKE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC7BH,OAAA;YAAIE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAuB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5DP,OAAA;YAAGE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnDP,OAAA;YAAGE,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxDP,OAAA;YAAGE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAIrC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACC,EAAA,GAnCIT,SAAmB;AAqCzB,eAAeA,SAAS;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}