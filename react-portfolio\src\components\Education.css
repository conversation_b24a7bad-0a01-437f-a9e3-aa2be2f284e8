.education {
  padding: 6rem 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.education-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: start;
}

.education-item {
  background: white;
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.education-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  transform: scaleX(0);
  transition: transform 0.3s ease;
  z-index: -1;
}

.education-item:hover::before {
  transform: scaleX(1);
}

.education-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.education-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.education-institution {
  font-size: 1.2rem;
  color: #555;
  margin-bottom: 0.5rem;
}

.education-date {
  font-size: 1.1rem;
  color: #777;
  margin-bottom: 1.5rem;
}

.education-description {
  font-size: 1rem;
  line-height: 1.8;
  color: #555;
}

@media (max-width: 768px) {
  .education-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .education-item {
    padding: 1.5rem;
  }
}
